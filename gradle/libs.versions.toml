# Releases: https://developer.android.com/jetpack/androidx/versions

[versions]
agp = "8.10.1"
coreSplashscreen = "1.0.1"
kotlin = "2.1.10"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
lifecycleRuntimeKtx = "2.9.1"
activityCompose = "1.10.1"
composeBom = "2025.06.00"
compose-material = "1.7.8"
material3-expressive = "1.4.0-alpha15" # alpha required for LoadingIndicator "1.3.2"
material3-adaptive = "1.1.0" # "1.2.0-alpha06"
glide = "4.16.0"
work = "2.10.1"
navigation = "2.9.0"
datastore = "1.1.7"
glance = "1.1.1"
coreAnimation = "1.0.0"
kotlinSerialization = "1.8.0"
kotlinSerializationPlugin = "2.1.10"
room = "2.7.1"
camerax = "1.4.2"
mlkit-barcode = "17.3.0"
desugar-jdk-libs = "2.1.5"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-core-splashscreen = { module = "androidx.core:core-splashscreen", version.ref = "coreSplashscreen" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-lifecycle-runtime-compose = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-material3-expressive = { group = "androidx.compose.material3", name = "material3", version.ref = "material3-expressive" }
androidx-material3-icons = { group = "androidx.compose.material3", module = "androidx.compose.material:material-icons-extended", version.ref = "compose-material" }
androidx-material3-adaptive = { group = "androidx.compose.material3.adaptive", name = "adaptive", version.ref = "material3-adaptive" }
androidx-window = { group = "androidx.window", name = "window", version = "1.4.0" }
androidx-window-core = { group = "androidx.window", name = "window-core", version = "1.4.0" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version = "33.15.0" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging" }
google-play-services-base = { module = "com.google.android.gms:play-services-base", version = "18.7.0" }
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }
glide-ksp = { group = "com.github.bumptech.glide", name = "ksp", version.ref = "glide" }
glide-okhttp3-integration = { group = "com.github.bumptech.glide", name = "okhttp3-integration", version.ref = "glide" }
androidx-work-runtime-ktx = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "work" }
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation" }
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "datastore" }
androidx-glance-appwidget = { group = "androidx.glance", name = "glance-appwidget", version.ref = "glance" }
androidx-glance-material3 = { group = "androidx.glance", name = "glance-material3", version.ref = "glance" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version = "2.2" }
androidx-core-animation = { group = "androidx.core", name = "core-animation", version.ref = "coreAnimation" }
androidx-media = { group = "androidx.media", name = "media", version = "1.7.0" }
kotlin-serialization = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-core", version.ref = "kotlinSerialization" }
kotlin-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinSerialization" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-camera-core = { group = "androidx.camera", name = "camera-core", version.ref = "camerax" }
androidx-camera-camera2 = { group = "androidx.camera", name = "camera-camera2", version.ref = "camerax" }
androidx-camera-lifecycle = { group = "androidx.camera", name = "camera-lifecycle", version.ref = "camerax" }
androidx-camera-view = { group = "androidx.camera", name = "camera-view", version.ref = "camerax" }
mlkit-barcode-scanning = { group = "com.google.mlkit", name = "barcode-scanning", version.ref = "mlkit-barcode" }
desugar-jdk-libs = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "desugar-jdk-libs" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version = "2.1.10-1.0.30" }
google-services = { id = "com.google.gms.google-services", version = "4.4.2" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlinSerializationPlugin" }
